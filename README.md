# CMS with Next.js 15 and Prisma

A modern Content Management System built with Next.js 15, Prisma, and Tiptap editor.

## Features

- **User Management**: Role-based access control (<PERSON><PERSON>, Editor, Author)
- **Post Management**: Create, edit, and publish blog posts with rich content
- **Tag System**: Organize posts with tags (many-to-many relationship)
- **Media Management**: Upload and manage images with local file storage
- **Rich Text Editor**: Tiptap editor with:
  - Bold, Italic formatting
  - Headings (H2, H3, H4)
  - Lists (bullet and numbered)
  - Blockquotes and code blocks
  - Image insertion (URL or file upload)
  - Text alignment (left, center, right)
- **API Routes**: RESTful API for all CRUD operations
- **Responsive Design**: Mobile-friendly admin interface

## Tech Stack

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: SQLite with Prisma ORM
- **Editor**: Tiptap (ProseMirror-based)
- **Authentication**: JWT tokens
- **File Upload**: Local storage in public/uploads

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up the database:
```bash
npm run db:migrate
npm run db:generate
```

3. Create an admin user:
```bash
npm run db:seed
```

This will create an admin user with:
- Email: <EMAIL>
- Password: admin123

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

### Admin Panel

Access the admin panel at `/admin/posts` after logging in.

### API Endpoints

#### Authentication
- `POST /api/auth/login` - Login user
- `POST /api/auth/register` - Register new user
- `GET /api/auth/me` - Get current user

#### Posts
- `GET /api/posts` - Get all posts (with pagination)
- `POST /api/posts` - Create new post
- `GET /api/posts/[id]` - Get single post
- `PUT /api/posts/[id]` - Update post
- `DELETE /api/posts/[id]` - Delete post

#### Tags
- `GET /api/tags` - Get all tags
- `POST /api/tags` - Create new tag
- `GET /api/tags/[id]` - Get single tag with posts
- `PUT /api/tags/[id]` - Update tag
- `DELETE /api/tags/[id]` - Delete tag

#### Media
- `POST /api/media/upload` - Upload file
- `GET /api/media` - Get all media files
- `GET /api/media/[id]` - Get single media file
- `DELETE /api/media/[id]` - Delete media file

## Database Schema

The application uses the following main models:

- **User**: Authentication and authorization
- **Post**: Blog posts with JSON content from Tiptap
- **Tag**: Post categorization
- **Media**: File upload metadata

## Development

### Database Operations

```bash
# Create and apply migration
npm run db:migrate

# Generate Prisma client
npm run db:generate

# Create admin user
npm run db:seed
```

### Code Formatting

```bash
# Check code style
npm run lint

# Format code
npm run format
```
