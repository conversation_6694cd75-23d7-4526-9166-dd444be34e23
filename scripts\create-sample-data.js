const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createSampleData() {
  try {
    // Create sample tags
    const tags = await Promise.all([
      prisma.tag.create({
        data: {
          name: 'Technology',
          slug: 'technology'
        }
      }),
      prisma.tag.create({
        data: {
          name: 'Web Development',
          slug: 'web-development'
        }
      }),
      prisma.tag.create({
        data: {
          name: 'JavaScript',
          slug: 'javascript'
        }
      }),
      prisma.tag.create({
        data: {
          name: 'React',
          slug: 'react'
        }
      }),
      prisma.tag.create({
        data: {
          name: 'Next.js',
          slug: 'nextjs'
        }
      })
    ])

    // Get admin user
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!adminUser) {
      console.log('Admin user not found. Please run npm run db:seed first.')
      return
    }

    // Sample Tiptap content
    const sampleContent = {
      type: 'doc',
      content: [
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Welcome to Our CMS' }]
        },
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: 'This is a sample blog post created with our ' },
            { type: 'text', marks: [{ type: 'bold' }], text: 'Next.js CMS' },
            { type: 'text', text: ' system. The content is stored as JSON and rendered using Tiptap.' }
          ]
        },
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: 'Here are some features of our CMS:' }
          ]
        },
        {
          type: 'bulletList',
          content: [
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [{ type: 'text', text: 'Rich text editing with Tiptap' }]
                }
              ]
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [{ type: 'text', text: 'Image upload and management' }]
                }
              ]
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [{ type: 'text', text: 'Tag-based organization' }]
                }
              ]
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [{ type: 'text', text: 'Role-based access control' }]
                }
              ]
            }
          ]
        },
        {
          type: 'blockquote',
          content: [
            {
              type: 'paragraph',
              content: [
                { type: 'text', text: 'This CMS is built with modern technologies like Next.js 15, Prisma, and Tailwind CSS.' }
              ]
            }
          ]
        },
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: 'You can also include code blocks:' }
          ]
        },
        {
          type: 'codeBlock',
          content: [
            { type: 'text', text: 'const greeting = "Hello, World!";\nconsole.log(greeting);' }
          ]
        }
      ]
    }

    // Create sample posts
    const posts = await Promise.all([
      prisma.post.create({
        data: {
          title: 'Getting Started with Next.js 15',
          content: sampleContent,
          status: 'PUBLISHED',
          authorId: adminUser.id,
          tags: {
            connect: [
              { id: tags[0].id }, // Technology
              { id: tags[1].id }, // Web Development
              { id: tags[4].id }  // Next.js
            ]
          }
        }
      }),
      prisma.post.create({
        data: {
          title: 'Building Modern Web Applications with React',
          content: {
            type: 'doc',
            content: [
              {
                type: 'paragraph',
                content: [
                  { type: 'text', text: 'React has revolutionized the way we build user interfaces. In this post, we\'ll explore the latest features and best practices.' }
                ]
              },
              {
                type: 'heading',
                attrs: { level: 2 },
                content: [{ type: 'text', text: 'Key Features' }]
              },
              {
                type: 'orderedList',
                content: [
                  {
                    type: 'listItem',
                    content: [
                      {
                        type: 'paragraph',
                        content: [{ type: 'text', text: 'Component-based architecture' }]
                      }
                    ]
                  },
                  {
                    type: 'listItem',
                    content: [
                      {
                        type: 'paragraph',
                        content: [{ type: 'text', text: 'Virtual DOM for performance' }]
                      }
                    ]
                  },
                  {
                    type: 'listItem',
                    content: [
                      {
                        type: 'paragraph',
                        content: [{ type: 'text', text: 'Rich ecosystem and community' }]
                      }
                    ]
                  }
                ]
              }
            ]
          },
          status: 'PUBLISHED',
          authorId: adminUser.id,
          tags: {
            connect: [
              { id: tags[0].id }, // Technology
              { id: tags[1].id }, // Web Development
              { id: tags[2].id }, // JavaScript
              { id: tags[3].id }  // React
            ]
          }
        }
      }),
      prisma.post.create({
        data: {
          title: 'Draft: Upcoming Features',
          content: {
            type: 'doc',
            content: [
              {
                type: 'paragraph',
                content: [
                  { type: 'text', text: 'This is a draft post about upcoming features we\'re planning to implement.' }
                ]
              }
            ]
          },
          status: 'DRAFT',
          authorId: adminUser.id,
          tags: {
            connect: [
              { id: tags[0].id } // Technology
            ]
          }
        }
      })
    ])

    console.log('Sample data created successfully!')
    console.log(`Created ${tags.length} tags and ${posts.length} posts.`)

  } catch (error) {
    console.error('Error creating sample data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSampleData()
