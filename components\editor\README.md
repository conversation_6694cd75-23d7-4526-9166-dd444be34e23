# WYSIWYG Editor Component

Komponen editor WYSIWYG custom yang telah diperbaiki dan diintegrasikan untuk aplikasi CMS Next.js 15.

## 🚀 Fitur Utama

### ✅ **Perbaikan ESLint yang Telah Dilakukan:**
- ✅ Menambahkan TypeScript interfaces yang proper
- ✅ Memperbaiki missing dependencies di useEffect hooks
- ✅ Menghapus unused imports (Type, Palette, Code icons)
- ✅ Memperbaiki event handler type annotations
- ✅ Menambahkan proper TypeScript types untuk refs dan state
- ✅ Menggunakan useCallback untuk optimasi performa
- ✅ Menambahkan forwardRef untuk akses method dari parent component
- ✅ Memperbaiki null safety dengan optional chaining

### 🎨 **Fitur Editor:**
- **Dual Mode**: Visual editor dan HTML code editor
- **Rich Text Formatting**: Bold, italic, underline, alignment
- **Image Upload**: Upload dan format gambar dengan berbagai posisi dan ukuran
- **Lists**: Bullet lists dan numbered lists
- **Quotes**: Blockquote formatting
- **Links**: Insert dan edit links
- **Undo/Redo**: History management
- **Responsive Design**: Bekerja baik di desktop dan mobile

## 📝 Cara Penggunaan

### Basic Usage

```tsx
import { useRef } from 'react'
import WYSIWYGEditor, { WYSIWYGEditorRef } from '@/components/editor/Editor'

function MyComponent() {
  const editorRef = useRef<WYSIWYGEditorRef>(null)
  const [content, setContent] = useState('')

  const handleSave = () => {
    const editorContent = editorRef.current?.getContent()
    console.log('Content:', editorContent)
  }

  return (
    <div>
      <WYSIWYGEditor
        ref={editorRef}
        initialContent={content}
        onChange={setContent}
        placeholder="Start writing..."
        className="min-h-[400px]"
      />
      <button onClick={handleSave}>Save</button>
    </div>
  )
}
```

### Props Interface

```tsx
interface WYSIWYGEditorProps {
  initialContent?: string;      // Konten awal (HTML string)
  onChange?: (content: string) => void;  // Callback saat konten berubah
  placeholder?: string;         // Placeholder text
  className?: string;          // CSS classes tambahan
}

interface WYSIWYGEditorRef {
  getContent: () => string;    // Mendapatkan konten HTML
  clearContent: () => void;    // Menghapus semua konten
}
```

## 🔧 Integrasi dengan CMS

### 1. **Create Post Page** (`app/admin/posts/new/page.tsx`)
```tsx
const editorRef = useRef<WYSIWYGEditorRef>(null)

const handleSubmit = async (e: React.FormEvent) => {
  const editorContent = editorRef.current?.getContent() || content
  // Submit editorContent to API
}

<WYSIWYGEditor
  ref={editorRef}
  initialContent={content}
  onChange={setContent}
  placeholder="Start writing your post..."
/>
```

### 2. **Edit Post Page** (`app/admin/posts/[id]/edit/page.tsx`)
```tsx
// Same pattern as create page
<WYSIWYGEditor
  ref={editorRef}
  initialContent={content}  // Loaded from API
  onChange={setContent}
  placeholder="Edit your post content..."
/>
```

### 3. **Content Renderer** (`components/editor/ContentRenderer.tsx`)
```tsx
// Automatically handles HTML content from WYSIWYG editor
<ContentRenderer content={post.content} />
```

## 🎯 Keunggulan vs BlockEditor (Tiptap)

| Fitur | WYSIWYG Editor | BlockEditor (Tiptap) |
|-------|----------------|---------------------|
| **Bundle Size** | ✅ Lebih kecil | ❌ Lebih besar |
| **Customization** | ✅ Full control | ❌ Terbatas |
| **Image Handling** | ✅ Advanced positioning | ❌ Basic |
| **HTML Output** | ✅ Clean HTML | ❌ JSON format |
| **Performance** | ✅ Faster | ❌ Slower |
| **Dependencies** | ✅ Minimal | ❌ Many packages |

## 🛠️ Technical Details

### **Teknologi yang Digunakan:**
- **React 19** dengan TypeScript
- **contentEditable** API untuk rich text editing
- **FileReader** API untuk image upload
- **document.execCommand** untuk text formatting (deprecated tapi masih supported)
- **Tailwind CSS** untuk styling
- **Next.js 15** compatibility

### **File Structure:**
```
components/editor/
├── Editor.tsx              # Main WYSIWYG editor component
├── ContentRenderer.tsx     # Read-only content renderer
└── README.md              # Documentation (this file)
```

## 🚨 Migration dari BlockEditor

Jika Anda sebelumnya menggunakan BlockEditor (Tiptap), berikut langkah migrasi:

1. **Update imports:**
```tsx
// Before
import BlockEditor from '@/components/editor/BlockEditor'

// After
import WYSIWYGEditor, { WYSIWYGEditorRef } from '@/components/editor/Editor'
```

2. **Add ref:**
```tsx
const editorRef = useRef<WYSIWYGEditorRef>(null)
```

3. **Update component usage:**
```tsx
// Before
<BlockEditor content={content} onChange={setContent} />

// After
<WYSIWYGEditor
  ref={editorRef}
  initialContent={content}
  onChange={setContent}
/>
```

4. **Update form submission:**
```tsx
// Get content from ref instead of state
const editorContent = editorRef.current?.getContent() || content
```

## 🎨 Styling

Editor menggunakan Tailwind CSS dan styled-jsx untuk styling. CSS classes utama:

- `.selected-image`: Styling untuk gambar yang dipilih
- `[contenteditable]`: Styling untuk area editor
- `.image-left`, `.image-right`, `.image-center`: Positioning gambar

## 🔮 Future Improvements

- [ ] Replace deprecated `document.execCommand` dengan modern alternatives
- [ ] Add table support
- [ ] Add emoji picker
- [ ] Add spell check
- [ ] Add collaborative editing
- [ ] Add export to PDF/Word
- [ ] Add more image editing options
- [ ] Add drag & drop untuk images
- [ ] Add auto-save functionality

## 📞 Support

Jika ada pertanyaan atau issues, silakan buat issue di repository atau hubungi tim development.
