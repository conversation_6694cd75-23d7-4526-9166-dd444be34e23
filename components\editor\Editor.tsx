import * as React from 'react';
import { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, AlignJustify,
  Image, Link, List, ListOrdered, Quote, Undo, Redo,
  Save, Trash2
} from 'lucide-react';

const WYSIWYGEditor = () => {
  const editorRef = useRef<HTMLDivElement>(null);
  const codeEditorRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedImage, setSelectedImage] = useState<HTMLImageElement | null>(null);
  const [showImageOptions, setShowImageOptions] = useState(false);
  const [editorMode, setEditorMode] = useState('visual'); // 'visual' or 'code'
  const [content, setContent] = useState(`
    <h2>Custom WYSIWYG Editor</h2>
    <p>Ini adalah editor WYSIWYG custom yang dibuat untuk Next.js 15 dengan fitur format gambar lengkap seperti WordPress Classic Editor.</p>
    <p><strong>Fitur yang tersedia:</strong></p>
    <ul>
      <li>Format teks (bold, italic, underline)</li>
      <li>Alignment (kiri, tengah, kanan)</li>
      <li>Upload dan format gambar</li>
      <li>Lists dan quotes</li>
      <li>Undo/Redo</li>
      <li>Edit HTML Code</li>
    </ul>
    <p>Coba upload gambar dan atur posisinya!</p>
  `);

  useEffect(() => {
    if (editorRef.current && editorMode === 'visual') {
      editorRef.current.innerHTML = content;
    }
    if (codeEditorRef.current && editorMode === 'code') {
      codeEditorRef.current.value = content;
    }
  }, [editorMode, content]);

interface ExecCommandFunction {
    (command: string, value?: string | null): void;
}

const execCommand: ExecCommandFunction = useCallback((command: string, value: string | null = null) => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);

    try {
        // Modern approach using Selection and Range APIs
        switch (command) {
            case 'bold':
                toggleInlineStyle('strong');
                break;
            case 'italic':
                toggleInlineStyle('em');
                break;
            case 'underline':
                toggleInlineStyle('u');
                break;
            case 'createLink':
                if (value) {
                    wrapSelectionWithElement('a', { href: value });
                }
                break;
            case 'insertHTML':
                if (value) {
                    insertHTMLAtSelection(value);
                }
                break;
            case 'formatBlock':
                if (value) {
                    formatBlock(value);
                }
                break;
            case 'fontSize':
                if (value) {
                    applyFontSize(value);
                }
                break;
            case 'foreColor':
                if (value) {
                    applyTextColor(value);
                }
                break;
            case 'insertUnorderedList':
                toggleList('ul');
                break;
            case 'insertOrderedList':
                toggleList('ol');
                break;
            case 'justifyLeft':
                applyTextAlign('left');
                break;
            case 'justifyCenter':
                applyTextAlign('center');
                break;
            case 'justifyRight':
                applyTextAlign('right');
                break;
            case 'justifyFull':
                applyTextAlign('justify');
                break;
            case 'undo':
            case 'redo':
                // These still work with execCommand as they're document-level operations
                document.execCommand(command, false);
                break;
            default:
                // Fallback to execCommand for unsupported commands (with warning suppression)
                try {
                    document.execCommand(command, false, value || undefined);
                } catch (e) {
                    console.warn(`Command ${command} not supported in modern implementation`);
                }
        }
    } catch (error) {
        console.error('Error executing command:', error);
        // Fallback to execCommand if modern approach fails
        try {
            document.execCommand(command, false, value || undefined);
        } catch (fallbackError) {
            console.error('Fallback execCommand also failed:', fallbackError);
        }
    }

    editorRef.current.focus();
}, []);

// Helper functions for modern editing operations
const toggleInlineStyle = useCallback((tagName: string) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = range.toString();

    if (selectedText) {
        // Check if selection is already wrapped in the tag
        const parentElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
            ? range.commonAncestorContainer.parentElement
            : range.commonAncestorContainer as Element;

        const existingTag = parentElement?.closest(tagName.toLowerCase());
        let targetElement: Element | null = null;

        if (existingTag && range.startContainer.parentElement === existingTag) {
            // Remove the formatting
            const textNode = document.createTextNode(existingTag.textContent || '');
            existingTag.parentNode?.replaceChild(textNode, existingTag);
            targetElement = textNode.parentElement;
        } else {
            // Apply the formatting
            const element = document.createElement(tagName);
            try {
                range.surroundContents(element);
                targetElement = element;
            } catch (e) {
                // If surroundContents fails, extract and wrap content
                const contents = range.extractContents();
                element.appendChild(contents);
                range.insertNode(element);
                targetElement = element;
            }
        }

        selection.removeAllRanges();
        const newRange = document.createRange();
        newRange.selectNodeContents(targetElement || range.commonAncestorContainer);
        selection.addRange(newRange);
    }
}, []);

const wrapSelectionWithElement = useCallback((tagName: string, attributes: Record<string, string> = {}) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = range.toString();

    if (selectedText) {
        const element = document.createElement(tagName);
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });

        try {
            range.surroundContents(element);
        } catch (e) {
            const contents = range.extractContents();
            element.appendChild(contents);
            range.insertNode(element);
        }
    }
}, []);

const insertHTMLAtSelection = useCallback((html: string) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    range.deleteContents();

    const div = document.createElement('div');
    div.innerHTML = html;
    const fragment = document.createDocumentFragment();

    while (div.firstChild) {
        fragment.appendChild(div.firstChild);
    }

    range.insertNode(fragment);
}, []);

const formatBlock = useCallback((tagName: string) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let blockElement = range.startContainer.nodeType === Node.TEXT_NODE
        ? range.startContainer.parentElement
        : range.startContainer as Element;

    // Find the closest block element
    while (blockElement && !['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV', 'BLOCKQUOTE'].includes(blockElement.tagName)) {
        blockElement = blockElement.parentElement;
    }

    if (blockElement && editorRef.current?.contains(blockElement)) {
        const newElement = document.createElement(tagName.toUpperCase());
        newElement.innerHTML = blockElement.innerHTML;
        blockElement.parentNode?.replaceChild(newElement, blockElement);
    }
}, []);

const applyFontSize = useCallback((size: string) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    if (range.toString()) {
        const span = document.createElement('span');
        const fontSizes = ['8px', '10px', '12px', '14px', '18px', '24px', '36px'];
        span.style.fontSize = fontSizes[parseInt(size) - 1] || '12px';

        try {
            range.surroundContents(span);
        } catch (e) {
            const contents = range.extractContents();
            span.appendChild(contents);
            range.insertNode(span);
        }
    }
}, []);

const applyTextColor = useCallback((color: string) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    if (range.toString()) {
        const span = document.createElement('span');
        span.style.color = color;

        try {
            range.surroundContents(span);
        } catch (e) {
            const contents = range.extractContents();
            span.appendChild(contents);
            range.insertNode(span);
        }
    }
}, []);

const toggleList = useCallback((listType: 'ul' | 'ol') => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let blockElement = range.startContainer.nodeType === Node.TEXT_NODE
        ? range.startContainer.parentElement
        : range.startContainer as Element;

    // Find the closest block element or list item
    while (blockElement && !['P', 'LI', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(blockElement.tagName)) {
        blockElement = blockElement.parentElement;
    }

    if (blockElement && editorRef.current?.contains(blockElement)) {
        const existingList = blockElement.closest('ul, ol');

        if (existingList) {
            // Convert list back to paragraph
            const p = document.createElement('p');
            p.innerHTML = blockElement.innerHTML;
            existingList.parentNode?.replaceChild(p, existingList);
        } else {
            // Convert to list
            const list = document.createElement(listType);
            const li = document.createElement('li');
            li.innerHTML = blockElement.innerHTML;
            list.appendChild(li);
            blockElement.parentNode?.replaceChild(list, blockElement);
        }
    }
}, []);

const applyTextAlign = useCallback((alignment: string) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let blockElement = range.startContainer.nodeType === Node.TEXT_NODE
        ? range.startContainer.parentElement
        : range.startContainer as Element;

    // Find the closest block element
    while (blockElement && !['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV', 'BLOCKQUOTE'].includes(blockElement.tagName)) {
        blockElement = blockElement.parentElement;
    }

    if (blockElement && editorRef.current?.contains(blockElement)) {
        (blockElement as HTMLElement).style.textAlign = alignment;
    }
}, []);

  const selectImage = useCallback((img: HTMLImageElement) => {
    // Remove previous selection
    const selectedElements = document.querySelectorAll('.selected-image');
    selectedElements.forEach(el => {
      el.classList.remove('selected-image');
    });

    img.classList.add('selected-image');
    setSelectedImage(img);
    setShowImageOptions(true);
  }, []);

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = document.createElement('img');
        img.src = e.target?.result as string;
        img.className = 'max-w-xs h-auto float-left mr-4 mb-2 cursor-pointer transition-opacity hover:opacity-80 select-none image-left';
        
        const handleImageClick = () => selectImage(img);
        const handleImageMouseDown = (mouseEvent: MouseEvent) => {
          mouseEvent.preventDefault();
          selectImage(img);
        };
        const handleImageSelectStart = () => false;
        
        img.addEventListener('click', handleImageClick);
        img.addEventListener('mousedown', handleImageMouseDown);
        img.addEventListener('selectstart', handleImageSelectStart);
        
        // Insert image at cursor position
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          range.deleteContents();
          range.insertNode(img);
          range.setStartAfter(img);
          range.setEndAfter(img);
          selection.removeAllRanges();
          selection.addRange(range);
        } else if (editorRef.current) {
          editorRef.current.appendChild(img);
        }
        
        if (editorRef.current) {
          editorRef.current.focus();
        }
      };
      reader.readAsDataURL(file);
    }
    // Reset file input
    if (event.target) {
      event.target.value = '';
    }
  }, [selectImage]);

  const formatImage = useCallback((alignment: string, size: string | null = null) => {
    if (!selectedImage) return;
    
    // Reset classes
    let className = selectedImage.className
      .replace(/image-\w+|float-\w+|mx-auto|block|inline/g, '')
      .replace('selected-image', '')
      .trim();
    
    className += ' max-w-full h-auto cursor-pointer transition-opacity hover:opacity-80 select-none';
    
    // Apply alignment with Tailwind classes
    switch (alignment) {
      case 'left':
        className += ' image-left float-left mr-4 mb-2';
        break;
      case 'center':
        className += ' image-center block mx-auto my-4';
        break;
      case 'right':
        className += ' image-right float-right ml-4 mb-2';
        break;
      default:
        break;
    }
    
    // Apply size with Tailwind classes
    if (size) {
      // Remove existing width classes
      className = className.replace(/max-w-\w+|w-\w+/g, '');
      
      switch (size) {
        case 'small':
          className += ' max-w-xs'; // 150px
          break;
        case 'medium':
          className += ' max-w-sm'; // 300px
          break;
        case 'large':
          className += ' max-w-2xl'; // 600px
          break;
        case 'full':
          className += ' w-full';
          break;
        default:
          break;
      }
    }
    
    selectedImage.className = className;
    setShowImageOptions(false);
    setSelectedImage(null);
  }, [selectedImage]);

  const insertHTML = useCallback((html: string) => {
    execCommand('insertHTML', html);
  }, [execCommand]);

  const switchToVisual = useCallback(() => {
    if (editorMode === 'code' && codeEditorRef.current) {
      const codeContent = codeEditorRef.current.value;
      setContent(codeContent);
      setEditorMode('visual');
      setTimeout(() => {
        if (editorRef.current) {
          editorRef.current.innerHTML = codeContent;
        }
      }, 10);
    } else {
      setEditorMode('visual');
    }
    setShowImageOptions(false);
    setSelectedImage(null);
  }, [editorMode]);

  const switchToCode = useCallback(() => {
    if (editorMode === 'visual' && editorRef.current) {
      const visualContent = editorRef.current.innerHTML;
      setContent(visualContent);
      setEditorMode('code');
      setTimeout(() => {
        if (codeEditorRef.current) {
          codeEditorRef.current.value = visualContent;
        }
      }, 10);
    } else {
      setEditorMode('code');
    }
    setShowImageOptions(false);
    setSelectedImage(null);
  }, [editorMode]);

  const getContent = useCallback(() => {
    let currentContent;
    if (editorMode === 'visual' && editorRef.current) {
      currentContent = editorRef.current.innerHTML;
    } else if (editorMode === 'code' && codeEditorRef.current) {
      currentContent = codeEditorRef.current.value;
    } else {
      currentContent = content;
    }
    
    console.log('Editor Content:', currentContent);
    alert('Konten telah disimpan ke console! Buka Developer Tools untuk melihat.');
    return currentContent;
  }, [editorMode, content]);

  const clearContent = useCallback(() => {
    if (editorMode === 'visual' && editorRef.current) {
      editorRef.current.innerHTML = '';
    } else if (editorMode === 'code' && codeEditorRef.current) {
      codeEditorRef.current.value = '';
    }
    setContent('');
    setSelectedImage(null);
    setShowImageOptions(false);
  }, [editorMode]);

  const handleEditorInput = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    setContent((e.target as HTMLDivElement).innerHTML);
  }, []);

  const handleCodeInput = useCallback((e: React.FormEvent<HTMLTextAreaElement>) => {
    setContent((e.target as HTMLTextAreaElement).value);
  }, []);

  const handleEditorMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    // If clicking on image, prevent default selection behavior
    if ((e.target as HTMLElement).tagName === 'IMG') {
      e.preventDefault();
      selectImage(e.target as HTMLImageElement);
    }
  }, [selectImage]);

  const handleEditorClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if ((e.target as HTMLElement).tagName === 'IMG') {
      selectImage(e.target as HTMLImageElement);
    } else {
      setShowImageOptions(false);
      setSelectedImage(null);
      // Remove visual selection from images
      const selectedElements = document.querySelectorAll('.selected-image');
      selectedElements.forEach(el => {
        el.classList.remove('selected-image');
      });
    }
  }, [selectImage]);

  const handleEditorSelectStart = useCallback((e: Event) => {
    // Prevent text selection from starting on image
    if ((e.target as HTMLElement).tagName === 'IMG') {
      e.preventDefault();
      return false;
    }
    return true;
  }, []);

  const handleCodeKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle tab for indentation
    if (e.key === 'Tab') {
      e.preventDefault();
      const target = e.target as HTMLTextAreaElement;
      const start = target.selectionStart;
      const end = target.selectionEnd;
      const { value } = target;

      target.value = value.substring(0, start) + '  ' + value.substring(end);
      target.selectionStart = start + 2;
      target.selectionEnd = start + 2;
    }
  }, []);

  const handleLinkInsert = useCallback(() => {
    const url = window.prompt('Masukkan URL link:');
    if (url) {
      execCommand('createLink', url);
    }
  }, [execCommand]);

  const ToolbarButton = ({ onClick, children, title, active = false }: {
    onClick: () => void;
    children: React.ReactNode;
    title: string;
    active?: boolean;
  }) => (
    <button
      type="button"
      onClick={onClick}
      title={title}
      className={`p-2 rounded hover:bg-gray-100 transition-colors ${
        active ? 'bg-blue-100 text-blue-600' : 'text-gray-600'
      }`}
    >
      {children}
    </button>
  );

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          Custom WYSIWYG Editor untuk Next.js 15
        </h1>
        <p className="text-gray-600">
          Editor WYSIWYG custom dengan fitur format gambar lengkap
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex border border-b-0 border-gray-300 rounded-t-lg bg-gray-100">
        <button
          type="button"
          onClick={switchToVisual}
          className={`px-6 py-2 font-medium transition-colors ${
            editorMode === 'visual' 
              ? 'bg-white text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          Visual
        </button>
        <button
          type="button"
          onClick={switchToCode}
          className={`px-6 py-2 font-medium transition-colors ${
            editorMode === 'code' 
              ? 'bg-white text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          HTML
        </button>
      </div>

      {/* Toolbar - only visible in visual mode */}
      {editorMode === 'visual' && (
        <div className="border border-gray-300 rounded-t-lg bg-gray-50 p-2">
          <div className="flex flex-wrap gap-1">
            {/* Text Formatting */}
            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton onClick={() => execCommand('bold')} title="Bold">
                <Bold size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('italic')} title="Italic">
                <Italic size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('underline')} title="Underline">
                <Underline size={16} />
              </ToolbarButton>
            </div>

            {/* Alignment */}
            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton onClick={() => execCommand('justifyLeft')} title="Align Left">
                <AlignLeft size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('justifyCenter')} title="Align Center">
                <AlignCenter size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('justifyRight')} title="Align Right">
                <AlignRight size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('justifyFull')} title="Justify">
                <AlignJustify size={16} />
              </ToolbarButton>
            </div>

            {/* Lists */}
            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton onClick={() => execCommand('insertUnorderedList')} title="Bullet List">
                <List size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('insertOrderedList')} title="Numbered List">
                <ListOrdered size={16} />
              </ToolbarButton>
            </div>

            {/* Insert */}
            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton 
                onClick={() => fileInputRef.current?.click()} 
                title="Insert Image"
              >
                <Image size={16} />
              </ToolbarButton>
              <ToolbarButton 
                onClick={handleLinkInsert} 
                title="Insert Link"
              >
                <Link size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('formatBlock', 'blockquote')} title="Quote">
                <Quote size={16} />
              </ToolbarButton>
            </div>

            {/* Undo/Redo */}
            <div className="flex gap-1">
              <ToolbarButton onClick={() => execCommand('undo')} title="Undo">
                <Undo size={16} />
              </ToolbarButton>
              <ToolbarButton onClick={() => execCommand('redo')} title="Redo">
                <Redo size={16} />
              </ToolbarButton>
            </div>
          </div>

          {/* Font Size and Headings */}
          <div className="flex gap-2 mt-2 pt-2 border-t border-gray-300">
            <select 
              onChange={(e) => execCommand('formatBlock', e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded text-sm"
              defaultValue=""
            >
              <option value="">Format</option>
              <option value="h1">Heading 1</option>
              <option value="h2">Heading 2</option>
              <option value="h3">Heading 3</option>
              <option value="p">Paragraph</option>
            </select>
            
            <select 
              onChange={(e) => execCommand('fontSize', e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded text-sm"
              defaultValue="3"
            >
              <option value="1">8pt</option>
              <option value="2">10pt</option>
              <option value="3">12pt</option>
              <option value="4">14pt</option>
              <option value="5">18pt</option>
              <option value="6">24pt</option>
              <option value="7">36pt</option>
            </select>

            <input
              type="color"
              onChange={(e) => execCommand('foreColor', e.target.value)}
              className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
              title="Text Color"
            />
          </div>
        </div>
      )}

      {/* Editor Area */}
      {editorMode === 'visual' ? (
        // Visual Editor
        <div
          ref={editorRef}
          contentEditable
          className="min-h-96 p-4 border-l border-r border-gray-300 bg-white outline-none leading-relaxed text-sm font-sans"
          onInput={handleEditorInput}
          onMouseDown={handleEditorMouseDown}
          onClick={handleEditorClick}
          role="textbox"
          aria-label="Visual Editor"
        />
      ) : (
        // Code Editor
        <textarea
          ref={codeEditorRef}
          className="min-h-96 p-4 border-l border-r border-gray-300 bg-white outline-none font-mono text-xs leading-relaxed resize-none"
          placeholder="Tulis HTML code di sini..."
          onInput={handleCodeInput}
          onKeyDown={handleCodeKeyDown}
          aria-label="HTML Code Editor"
        />
      )}

      {/* Image Options Panel - only visible in visual mode */}
      {editorMode === 'visual' && showImageOptions && selectedImage && (
        <div className="border border-blue-300 bg-blue-50 p-4 rounded">
          <h4 className="font-semibold text-blue-800 mb-3">Format Gambar</h4>
          
          <div className="flex flex-wrap gap-2 mb-3">
            <strong className="text-sm text-blue-700 w-full mb-1">Posisi:</strong>
            <button
              type="button"
              onClick={() => formatImage('left')}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
            >
              Rata Kiri
            </button>
            <button
              type="button"
              onClick={() => formatImage('center')}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
            >
              Rata Tengah
            </button>
            <button
              type="button"
              onClick={() => formatImage('right')}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
            >
              Rata Kanan
            </button>
          </div>

          <div className="flex flex-wrap gap-2">
            <strong className="text-sm text-blue-700 w-full mb-1">Ukuran + Posisi:</strong>
            <button
              type="button"
              onClick={() => formatImage('left', 'small')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Kiri - Kecil
            </button>
            <button
              type="button"
              onClick={() => formatImage('left', 'medium')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Kiri - Sedang
            </button>
            <button
              type="button"
              onClick={() => formatImage('center', 'medium')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Tengah - Sedang
            </button>
            <button
              type="button"
              onClick={() => formatImage('center', 'large')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Tengah - Besar
            </button>
            <button
              type="button"
              onClick={() => formatImage('right', 'small')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Kanan - Kecil
            </button>
            <button
              type="button"
              onClick={() => formatImage('right', 'medium')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Kanan - Sedang
            </button>
          </div>
        </div>
      )}

      {/* Bottom border */}
      <div className="border-b border-l border-r border-gray-300 rounded-b-lg h-2 bg-gray-50" />

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />

      {/* Action buttons */}
      <div className="flex gap-4 mt-4">
        <button
          type="button"
          onClick={getContent}
          className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Save size={16} />
          Simpan Konten
        </button>
        <button
          type="button"
          onClick={clearContent}
          className="flex items-center gap-2 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          <Trash2 size={16} />
          Hapus Semua
        </button>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-gray-100 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Cara Menggunakan:</h3>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• <strong>Tab Visual:</strong> Mode WYSIWYG untuk editing visual</li>
          <li>• <strong>Tab HTML:</strong> Mode code untuk editing HTML langsung</li>
          <li>• <strong>Format Teks:</strong> Pilih teks dan klik tombol formatting di toolbar</li>
          <li>• <strong>Upload Gambar:</strong> Klik tombol gambar di toolbar dan pilih file</li>
          <li>• <strong>Format Gambar:</strong> Klik gambar yang sudah diupload untuk menampilkan opsi format</li>
          <li>• <strong>Edit HTML:</strong> Switch ke tab HTML untuk mengedit kode secara langsung</li>
          <li>• <strong>Syntax:</strong> HTML code otomatis tersinkronisasi antara kedua mode</li>
        </ul>
      </div>

      {/* CSS Styles - Tailwind-compatible custom styles */}
      <style>{`
        .selected-image {
          @apply border-2 border-blue-500 shadow-lg;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
        }
        
        [contenteditable] img.image-left {
          @apply float-left mr-4 mb-2;
        }
        
        [contenteditable] img.image-right {
          @apply float-right ml-4 mb-2;
        }
        
        [contenteditable] img.image-center {
          @apply block mx-auto my-4;
          float: none !important;
        }
        
        [contenteditable] p {
          @apply my-2;
          overflow: hidden;
        }
        
        [contenteditable] blockquote {
          @apply border-l-4 border-gray-300 pl-4 ml-0 italic text-gray-600 my-3;
          overflow: hidden;
        }
        
        [contenteditable] h1 {
          @apply text-3xl font-bold my-4;
          overflow: hidden;
        }
        
        [contenteditable] h2 {
          @apply text-2xl font-bold my-3;
          overflow: hidden;
        }
        
        [contenteditable] h3 {
          @apply text-xl font-bold my-3;
          overflow: hidden;
        }
        
        [contenteditable] ul, [contenteditable] ol {
          @apply pl-6 my-3;
          overflow: hidden;
        }
        
        [contenteditable] li {
          @apply my-1;
        }
        
        [contenteditable]:after,
        [contenteditable] div:after {
          content: "";
          display: table;
          clear: both;
        }
      `}</style>
    </div>
  );
};

export default WYSIWYGEditor;