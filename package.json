{"name": "cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "node scripts/create-admin.js", "db:sample": "node scripts/create-sample-data.js"}, "dependencies": {"@prisma/client": "^6.16.2", "@tiptap/extension-blockquote": "^3.4.4", "@tiptap/extension-bullet-list": "^3.4.4", "@tiptap/extension-code-block": "^3.4.4", "@tiptap/extension-heading": "^3.4.4", "@tiptap/extension-image": "^3.4.4", "@tiptap/extension-list-item": "^3.4.4", "@tiptap/extension-ordered-list": "^3.4.4", "@tiptap/extension-text-align": "^3.4.4", "@tiptap/pm": "^3.4.4", "@tiptap/react": "^3.4.4", "@tiptap/starter-kit": "^3.4.4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "multer": "^2.0.2", "next": "15.5.3", "next-auth": "^4.24.11", "prisma": "^6.16.2", "react": "19.1.0", "react-dom": "19.1.0", "uuid": "^13.0.0"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}