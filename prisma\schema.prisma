// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enum for user roles
enum Role {
  ADMIN
  EDITOR
  AUTHOR
}

// Enum for post status
enum PostStatus {
  DRAFT
  PUBLISHED
}

// User model for authentication and authorization
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      Role     @default(AUTHOR)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts Post[]

  @@map("users")
}

// Post model for blog posts/articles
model Post {
  id        String     @id @default(cuid())
  title     String
  content   Json       // Tiptap JSON content
  status    PostStatus @default(DRAFT)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // Relations
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  tags     Tag[]

  @@map("posts")
}

// Tag model for categorizing posts
model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts Post[]

  @@map("tags")
}

// Media model for file uploads
model Media {
  id        String   @id @default(cuid())
  filename  String
  originalName String
  mimeType  String
  size      Int
  url       String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("media")
}
