import Link from 'next/link'
import { prisma } from '@/lib/prisma'

async function getPosts() {
  try {
    const posts = await prisma.post.findMany({
      where: { status: 'PUBLISHED' },
      include: {
        author: {
          select: { id: true, name: true, email: true }
        },
        tags: true
      },
      orderBy: { createdAt: 'desc' }
    })

    return posts
  } catch (error) {
    console.error('Error fetching posts:', error)
    return []
  }
}

export default async function PostsPage() {
  const posts = await getPosts()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Navigation */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <div>
                  <h1 className="text-lg font-bold text-gray-900">BPMP</h1>
                  <p className="text-xs text-gray-600">Provinsi Kalimantan Timur</p>
                </div>
              </Link>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="/" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Home
              </Link>
              <Link href="/posts" className="text-blue-700 hover:text-blue-900 px-3 py-2 text-sm font-bold">
                Artikel
              </Link>
              <Link href="/admin/posts" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Admin
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-black mb-4">
            Semua Artikel
          </h1>
          <p className="text-xl text-gray-800 font-medium">
            Kumpulan artikel dan berita terbaru seputar pendidikan di Kalimantan Timur
          </p>
        </div>

        {posts.length > 0 ? (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {posts.map((post) => (
              <article
                key={post.id}
                className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="p-6">
                  <h2 className="text-xl font-bold text-black mb-2">
                    <Link
                      href={`/posts/${post.id}`}
                      className="hover:text-blue-600 transition-colors"
                    >
                      {post.title}
                    </Link>
                  </h2>
                  
                  <div className="text-sm text-gray-800 mb-3 font-medium">
                    <span>By {post.author.name || post.author.email}</span>
                    <span className="mx-2">•</span>
                    <time dateTime={post.createdAt.toISOString()}>
                      {post.createdAt.toLocaleDateString('id-ID', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </time>
                  </div>

                  {post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {post.tags.map((tag) => (
                        <span
                          key={tag.id}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {tag.name}
                        </span>
                      ))}
                    </div>
                  )}

                  <Link
                    href={`/posts/${post.id}`}
                    className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    Baca selengkapnya →
                  </Link>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📝</div>
            <p className="text-gray-800 text-lg mb-6 font-medium">Belum ada artikel yang dipublikasikan.</p>
            <Link
              href="/admin/posts/new"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              Buat Artikel Pertama
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
