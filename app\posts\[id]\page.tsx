import { notFound } from 'next/navigation'
import ContentRenderer from '@/components/editor/ContentRenderer'
import { prisma } from '@/lib/prisma'

interface PostPageProps {
  params: { id: string }
}

async function getPost(id: string) {
  try {
    const post = await prisma.post.findUnique({
      where: { 
        id,
        status: 'PUBLISHED' // Only show published posts
      },
      include: {
        author: {
          select: { id: true, name: true, email: true }
        },
        tags: true
      }
    })

    return post
  } catch (error) {
    console.error('Error fetching post:', error)
    return null
  }
}

export default async function PostPage({ params }: PostPageProps) {
  const { id } = await params
  const post = await getPost(id)

  if (!post) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link href="/" className="text-blue-600 hover:text-blue-800 font-medium">
            ← Kembali ke Beranda
          </Link>
        </div>
        <article className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-8">
            <header className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {post.title}
              </h1>
              
              <div className="flex items-center text-sm text-gray-600 mb-4">
                <span>By {post.author.name || post.author.email}</span>
                <span className="mx-2">•</span>
                <time dateTime={post.createdAt.toISOString()}>
                  {post.createdAt.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
              </div>

              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag.name}
                    </span>
                  ))}
                </div>
              )}
            </header>

            <div className="prose prose-lg max-w-none">
              <ContentRenderer content={post.content} />
            </div>
          </div>
        </article>
      </div>
    </div>
  )
}
