import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

export interface UploadResult {
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
}

export async function saveUploadedFile(file: File): Promise<UploadResult> {
  const bytes = await file.arrayBuffer()
  const buffer = Buffer.from(bytes)

  // Create uploads directory if it doesn't exist
  const uploadsDir = path.join(process.cwd(), 'public', 'uploads')
  if (!existsSync(uploadsDir)) {
    await mkdir(uploadsDir, { recursive: true })
  }

  // Generate unique filename
  const fileExtension = path.extname(file.name)
  const filename = `${uuidv4()}${fileExtension}`
  const filepath = path.join(uploadsDir, filename)

  // Save file
  await writeFile(filepath, buffer)

  return {
    filename,
    originalName: file.name,
    mimeType: file.type,
    size: file.size,
    url: `/uploads/${filename}`
  }
}

export function isValidImageType(mimeType: string): boolean {
  const validTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ]
  return validTypes.includes(mimeType)
}

export function isValidFileSize(size: number, maxSizeMB: number = 10): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  return size <= maxSizeBytes
}
