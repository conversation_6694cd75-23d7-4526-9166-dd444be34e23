@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ffffff;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Global text color improvements - High contrast */
h1, h2, h3, h4, h5, h6 {
  color: #000000 !important;
  font-weight: 700;
}

p, span, div, li {
  color: #1f2937 !important;
}

/* Ensure very dark text in light mode */
@media (prefers-color-scheme: light) {
  body {
    color: #000000;
  }

  h1, h2, h3, h4, h5, h6 {
    color: #000000 !important;
  }

  p, span, div, li {
    color: #1f2937 !important;
  }
}

/* High contrast for specific elements */
.text-gray-900 {
  color: #000000 !important;
}

.text-gray-800 {
  color: #1f2937 !important;
}

.text-gray-700 {
  color: #374151 !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

/* Tiptap Editor Styles - High Contrast */
.ProseMirror {
  outline: none;
  color: #000000 !important;
  background: #ffffff;
}

.ProseMirror p {
  margin: 1em 0;
  color: #000000 !important;
}

.ProseMirror h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 1.5em 0 0.5em 0;
  color: #000000 !important;
}

.ProseMirror h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin: 1.25em 0 0.5em 0;
  color: #000000 !important;
}

.ProseMirror h4 {
  font-size: 1.1em;
  font-weight: bold;
  margin: 1.1em 0 0.5em 0;
  color: #000000 !important;
}

.ProseMirror ul, .ProseMirror ol {
  margin: 1em 0;
  padding-left: 1.5em;
  color: #000000 !important;
}

.ProseMirror li {
  color: #000000 !important;
}

.ProseMirror blockquote {
  margin: 1.5em 0;
  padding-left: 1em;
  border-left: 4px solid #374151;
  font-style: italic;
  color: #374151 !important;
  background: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

.ProseMirror pre {
  background: #1f2937;
  color: #ffffff !important;
  border-radius: 0.375rem;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  margin: 1em 0;
  overflow-x: auto;
}

.ProseMirror code {
  background: #1f2937;
  color: #ffffff !important;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1em 0;
}

.ProseMirror img.ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
}
