import Link from 'next/link'
import { prisma } from '@/lib/prisma'

async function getPosts() {
  try {
    const posts = await prisma.post.findMany({
      where: { status: 'PUBLISHED' },
      include: {
        author: {
          select: { id: true, name: true, email: true }
        },
        tags: true
      },
      orderBy: { createdAt: 'desc' },
      take: 6
    })

    return posts
  } catch (error) {
    console.error('Error fetching posts:', error)
    return []
  }
}

export default async function Home() {
  const posts = await getPosts()

  return (
    <div className="min-h-screen bg-white">
      {/* Header Navigation */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <div>
                  <h1 className="text-lg font-bold text-gray-900">BPMP</h1>
                  <p className="text-xs text-gray-600">Provinsi Kalimantan Timur</p>
                </div>
              </div>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="/" className="text-blue-700 hover:text-blue-900 px-3 py-2 text-sm font-semibold">
                Home
              </Link>
              <Link href="/profile" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Profile
              </Link>
              <Link href="/publikasi" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Publikasi
              </Link>
              <Link href="/layanan" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Layanan
              </Link>
              <Link href="/akuntabilitas" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Akuntabilitas
              </Link>
              <Link href="/pengaduan" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Pengaduan
              </Link>
              <Link href="/kontak" className="text-gray-900 hover:text-blue-700 px-3 py-2 text-sm font-medium">
                Kontak Kami
              </Link>
            </nav>
            <div className="md:hidden">
              <button className="text-gray-700 hover:text-blue-600">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white">
        <div className="absolute inset-0 bg-black opacity-30"></div>
        <div
          className="relative bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><defs><pattern id="people" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="20" cy="20" r="15" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="60" r="12" fill="rgba(255,255,255,0.08)"/><circle cx="50" cy="80" r="10" fill="rgba(255,255,255,0.06)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23people)"/></svg>')`
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center">
            <p className="text-xl mb-4 font-medium text-white">Selamat Datang Di</p>
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-lg">
              Balai Penjaminan Mutu Pendidikan
            </h1>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white drop-shadow-lg">
              Provinsi Kalimantan Timur
            </h2>
            <p className="text-xl mb-8 font-medium text-white">
              Kementerian Pendidikan, Kebudayaan, Riset, dan Teknologi
            </p>
            <Link
              href="/posts"
              className="inline-flex items-center px-8 py-4 bg-orange-600 hover:bg-orange-700 text-white font-bold rounded-full transition-colors shadow-lg text-lg"
            >
              Visi & Misi →
            </Link>
          </div>
        </div>
      </section>

      {/* Service Cards */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="bg-white rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-black mb-2">Penjaminan Mutu</h3>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-black mb-2">Standar Pendidikan</h3>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-black mb-2">Evaluasi Pendidikan</h3>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-black mb-2">Fasilitasi Peningkatan</h3>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-blue-600 mb-6">
                Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur
              </h2>
              <p className="text-black mb-6 leading-relaxed font-medium">
                Unit Pelaksana Teknis Balai Penjaminan Mutu Pendidikan – Kementerian
                Pendidikan, Kebudayaan, Riset, dan Teknologi yang mempunyai tugas melaksanakan
                penjaminan mutu pendidikan dasar dan menengah, evaluasi hasil belajar,
                fasilitasi peningkatan mutu pendidikan, dan pengembangan model pembelajaran
                inovatif di Provinsi Kalimantan Timur.
              </p>
              <Link
                href="/about"
                className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              >
                Selengkapnya →
              </Link>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl p-8 h-80 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-black">Pendidikan Berkualitas</h3>
                  <p className="text-gray-800 mt-2 font-medium">Untuk Masa Depan Kalimantan Timur</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Posts Section */}
      {posts.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-black mb-4">Berita & Artikel Terbaru</h2>
              <p className="text-xl text-gray-800 font-medium">Informasi terkini seputar pendidikan di Kalimantan Timur</p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {posts.map((post) => (
                <article
                  key={post.id}
                  className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                >
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-black mb-2">
                      <Link
                        href={`/posts/${post.id}`}
                        className="hover:text-blue-600 transition-colors"
                      >
                        {post.title}
                      </Link>
                    </h3>

                    <div className="text-sm text-gray-800 mb-3 font-medium">
                      <span>By {post.author.name || post.author.email}</span>
                      <span className="mx-2">•</span>
                      <time dateTime={post.createdAt.toISOString()}>
                        {post.createdAt.toLocaleDateString('id-ID', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </time>
                    </div>

                    {post.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.map((tag) => (
                          <span
                            key={tag.id}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag.name}
                          </span>
                        ))}
                      </div>
                    )}

                    <Link
                      href={`/posts/${post.id}`}
                      className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                    >
                      Baca selengkapnya →
                    </Link>
                  </div>
                </article>
              ))}
            </div>

            <div className="text-center mt-12">
              <Link
                href="/posts"
                className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              >
                Lihat Semua Artikel
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-xl">B</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">BPMP Provinsi Kalimantan Timur</h3>
                  <p className="text-gray-200">Balai Penjaminan Mutu Pendidikan</p>
                </div>
              </div>
              <p className="text-gray-200 mb-4">
                Kementerian Pendidikan, Kebudayaan, Riset, dan Teknologi
              </p>
              <p className="text-gray-200">
                Jl. Basuki Rahmat No. 123, Samarinda, Kalimantan Timur 75124
              </p>
            </div>

            <div>
              <h4 className="text-lg font-bold mb-4 text-white">Layanan</h4>
              <ul className="space-y-2">
                <li><Link href="/penjaminan-mutu" className="text-gray-200 hover:text-white font-medium">Penjaminan Mutu</Link></li>
                <li><Link href="/evaluasi" className="text-gray-200 hover:text-white font-medium">Evaluasi Pendidikan</Link></li>
                <li><Link href="/fasilitasi" className="text-gray-200 hover:text-white font-medium">Fasilitasi Peningkatan</Link></li>
                <li><Link href="/pengembangan" className="text-gray-200 hover:text-white font-medium">Pengembangan Model</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-bold mb-4 text-white">Kontak</h4>
              <ul className="space-y-2">
                <li className="text-gray-200">
                  <span className="font-bold text-white">Telepon:</span> (0541) 123-4567
                </li>
                <li className="text-gray-200">
                  <span className="font-bold text-white">Email:</span> <EMAIL>
                </li>
                <li className="text-gray-200">
                  <span className="font-bold text-white">Fax:</span> (0541) 123-4568
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-600 mt-8 pt-8 text-center">
            <p className="text-gray-200 font-medium">
              © 2024 BPMP Provinsi Kalimantan Timur. All rights reserved.
            </p>
            <div className="mt-4">
              <Link
                href="/admin/posts"
                className="text-gray-300 hover:text-white text-sm font-medium"
              >
                Admin Panel
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
